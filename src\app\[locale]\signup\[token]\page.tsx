'use client'

import { useState, useEffect, use } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { userDraftService } from '@/lib/user-draft-service'
import type { UserDraft } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import toast from 'react-hot-toast'

export default function SignupPage({ 
  params 
}: { 
  params: Promise<{ locale: string; token: string }> 
}) {
  const { locale, token } = use(params)
  const router = useRouter()
  const t = useTranslations()
  
  const [loading, setLoading] = useState(false)
  const [fetchingDraft, setFetchingDraft] = useState(true)
  const [draft, setDraft] = useState<UserDraft | null>(null)
  const [formData, setFormData] = useState({
    full_name: '',
    account_number: '',
    job_duties: '',
    password: '',
    confirmPassword: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    const fetchDraft = async () => {
      try {
        const draftData = await userDraftService.getDraftByToken(token)
        if (draftData) {
          setDraft(draftData)
          setFormData(prev => ({
            ...prev,
            full_name: draftData.full_name,
            account_number: draftData.account_number || '',
            job_duties: draftData.job_duties || ''
          }))
        }
      } catch (error) {
        console.error('Error fetching draft:', error)
      } finally {
        setFetchingDraft(false)
      }
    }

    fetchDraft()
  }, [token])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Full name is required'
    }

    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters'
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      const response = await fetch('/api/users/complete-registration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          password: formData.password,
          full_name: formData.full_name.trim(),
          account_number: formData.account_number.trim() || undefined,
          job_duties: formData.job_duties.trim() || undefined
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Registration failed')
      }

      toast.success(result.message || 'Registration completed successfully!')
      
      // Redirect to sign in page
      router.push(`/${locale}/auth/signin?message=registration-complete`)
      
    } catch (error: any) {
      toast.error(`Registration failed: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  if (fetchingDraft) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-lg text-gray-600">Loading invitation...</div>
      </div>
    )
  }

  if (!draft) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Invalid Invitation
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              This invitation link is invalid or has expired. Please contact your administrator for a new invitation.
            </p>
            <div className="mt-6">
              <Button onClick={() => router.push(`/${locale}/auth/signin`)}>
                Go to Sign In
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Complete Your Registration
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            You've been invited to join <strong>{draft.organization?.name}</strong>
          </p>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Invitation Details
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p><strong>Email:</strong> {draft.email}</p>
                <p><strong>Organization:</strong> {draft.organization?.name}</p>
                {draft.facility && (
                  <p><strong>Facility:</strong> {draft.facility.name}</p>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="full_name" className="block text-sm font-medium text-gray-700">
                Full Name *
              </label>
              <input
                id="full_name"
                name="full_name"
                type="text"
                required
                value={formData.full_name}
                onChange={(e) => setFormData(prev => ({ ...prev, full_name: e.target.value }))}
                className={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
                  errors.full_name ? 'border-red-300' : 'border-gray-300'
                } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500 focus:z-10 sm:text-sm`}
                placeholder="Enter your full name"
                disabled={loading}
              />
              {errors.full_name && (
                <p className="mt-1 text-sm text-red-600">{errors.full_name}</p>
              )}
            </div>

            <div>
              <label htmlFor="account_number" className="block text-sm font-medium text-gray-700">
                Account Number
              </label>
              <input
                id="account_number"
                name="account_number"
                type="text"
                value={formData.account_number}
                onChange={(e) => setFormData(prev => ({ ...prev, account_number: e.target.value }))}
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500 focus:z-10 sm:text-sm"
                placeholder="Enter account number (optional)"
                disabled={loading}
              />
            </div>

            <div>
              <label htmlFor="job_duties" className="block text-sm font-medium text-gray-700">
                Job Duties
              </label>
              <textarea
                id="job_duties"
                name="job_duties"
                rows={3}
                value={formData.job_duties}
                onChange={(e) => setFormData(prev => ({ ...prev, job_duties: e.target.value }))}
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500 focus:z-10 sm:text-sm"
                placeholder="Describe your job duties (optional)"
                disabled={loading}
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password *
              </label>
              <input
                id="password"
                name="password"
                type="password"
                required
                value={formData.password}
                onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                className={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
                  errors.password ? 'border-red-300' : 'border-gray-300'
                } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500 focus:z-10 sm:text-sm`}
                placeholder="Create a password"
                disabled={loading}
              />
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password}</p>
              )}
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                Confirm Password *
              </label>
              <input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                required
                value={formData.confirmPassword}
                onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                className={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
                  errors.confirmPassword ? 'border-red-300' : 'border-gray-300'
                } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500 focus:z-10 sm:text-sm`}
                placeholder="Confirm your password"
                disabled={loading}
              />
              {errors.confirmPassword && (
                <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
              )}
            </div>
          </div>

          <div>
            <Button
              type="submit"
              className="group relative w-full flex justify-center"
              disabled={loading}
            >
              {loading ? 'Creating Account...' : 'Complete Registration'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
