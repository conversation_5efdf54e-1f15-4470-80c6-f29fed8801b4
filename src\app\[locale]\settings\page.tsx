'use client'

import React, { useState, useEffect, use } from 'react'
import { useTranslations } from 'next-intl'
import { useUser } from '@/components/auth/user-provider'
import { Button } from '@/components/ui/button'
import { DataTable } from '@/components/ui/DataTable'
import { supabase, type Organization, type Facility, type Workplace, type User } from '@/lib/supabase'
import toast from 'react-hot-toast'
import { PencilIcon, KeyIcon, UserIcon, UserPlusIcon, UserMinusIcon, BuildingOfficeIcon, MapPinIcon, TrashIcon, EyeIcon, PlusIcon } from '@heroicons/react/24/outline'
import { ConfirmDialog } from '@/components/ui/ConfirmDialog'
import { OrganizationForm } from '@/components/forms/OrganizationForm'
import { FacilityForm } from '@/components/forms/FacilityForm'
import { WorkplaceForm } from '@/components/forms/WorkplaceForm'
import { UserForm } from '@/components/forms/UserForm'

export default function SettingsPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = use(params)
  const t = useTranslations()
  const { user } = useUser()
  
  // Data state
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [facilities, setFacilities] = useState<Facility[]>([])
  const [workplaces, setWorkplaces] = useState<Workplace[]>([])
  const [users, setUsers] = useState<(User & { organization?: Organization })[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'profile' | 'organizations' | 'facilities' | 'workplaces' | 'users'>('profile')
  const [selectedFacility, setSelectedFacility] = useState<string>('')

  // Modal states
  const [showOrganizationForm, setShowOrganizationForm] = useState(false)
  const [showFacilityForm, setShowFacilityForm] = useState(false)
  const [showWorkplaceForm, setShowWorkplaceForm] = useState(false)
  const [showUserForm, setShowUserForm] = useState(false)
  const [editingOrganization, setEditingOrganization] = useState<Organization | null>(null)
  const [editingFacility, setEditingFacility] = useState<Facility | null>(null)
  const [editingWorkplace, setEditingWorkplace] = useState<Workplace | null>(null)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create')
  
  // Confirmation dialog states
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [deleteTarget, setDeleteTarget] = useState<{
    type: 'organization' | 'facility' | 'workplace' | 'user'
    id: string
    name: string
  } | null>(null)
  const [deleteLoading, setDeleteLoading] = useState(false)

  useEffect(() => {
    if (user) {
      fetchData()
    }
  }, [user])

  const fetchData = React.useCallback(async () => {
    try {
      // Fetch organizations
      const { data: orgsData, error: orgsError } = await supabase
        .from('organizations')
        .select('*')
        .order('name')

      if (orgsError && orgsError.code !== '42P01') {
        throw orgsError
      }
      setOrganizations(orgsData || [])

      // Fetch facilities
      const { data: facilitiesData, error: facilitiesError } = await supabase
        .from('facilities')
        .select(`
          *,
          organization:organizations(name)
        `)
        .order('created_at', { ascending: false })

      if (facilitiesError && facilitiesError.code !== '42P01') {
        throw facilitiesError
      }
      setFacilities(facilitiesData || [])

      // Fetch workplaces
      const { data: workplacesData, error: workplacesError } = await supabase
        .from('workplaces')
        .select(`
          *,
          facility:facilities(name, organization_id)
        `)
        .order('created_at', { ascending: false })

      if (workplacesError && workplacesError.code !== '42P01') {
        throw workplacesError
      }
      setWorkplaces(workplacesData || [])

      // Fetch users in user's organizations
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select(`
          *,
          user_roles!inner(
            organization_id,
            organizations(name)
          )
        `)
        .order('created_at', { ascending: false })

      if (usersError && usersError.code !== '42P01') {
        throw usersError
      }

      // Transform users data to include organization info
      const transformedUsers = (usersData || []).map(user => ({
        ...user,
        organization: (user as any).user_roles?.[0]?.organizations
      }))
      setUsers(transformedUsers)
    } catch (error) {
      console.warn('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  // Modal handlers - using useCallback to prevent unnecessary re-renders
  const handleCreateOrganization = React.useCallback(() => {
    setFormMode('create')
    setEditingOrganization(null)
    setShowOrganizationForm(true)
  }, [])

  const handleEditOrganization = React.useCallback((organization: Organization) => {
    setFormMode('edit')
    setEditingOrganization(organization)
    setShowOrganizationForm(true)
  }, [])

  const handleCreateFacility = React.useCallback(() => {
    setFormMode('create')
    setEditingFacility(null)
    setShowFacilityForm(true)
  }, [])

  const handleEditFacility = React.useCallback((facility: Facility) => {
    setFormMode('edit')
    setEditingFacility(facility)
    setShowFacilityForm(true)
  }, [])

  const handleCreateWorkplace = React.useCallback(() => {
    setFormMode('create')
    setEditingWorkplace(null)
    setShowWorkplaceForm(true)
  }, [])

  const handleEditWorkplace = React.useCallback((workplace: Workplace) => {
    setFormMode('edit')
    setEditingWorkplace(workplace)
    setShowWorkplaceForm(true)
  }, [])

  const handleCreateUser = React.useCallback(() => {
    setFormMode('create')
    setEditingUser(null)
    setShowUserForm(true)
  }, [])

  const handleEditUser = React.useCallback((user: User) => {
    setFormMode('edit')
    setEditingUser(user)
    setShowUserForm(true)
  }, [])

  // Delete handlers
  const handleDeleteRequest = React.useCallback((type: 'organization' | 'facility' | 'workplace' | 'user', id: string, name: string) => {
    setDeleteTarget({ type, id, name })
    setShowDeleteConfirm(true)
  }, [])

  const handleDeleteConfirm = React.useCallback(async () => {
    if (!deleteTarget) return

    setDeleteLoading(true)
    try {
      const { error } = await supabase
        .from(`${deleteTarget.type === 'organization' ? 'organizations' :
               deleteTarget.type === 'facility' ? 'facilities' :
               deleteTarget.type === 'workplace' ? 'workplaces' : 'users'}`)
        .delete()
        .eq('id', deleteTarget.id)

      if (error) throw error

      await fetchData()
      toast.success(`${deleteTarget.type.charAt(0).toUpperCase() + deleteTarget.type.slice(1)} deleted successfully`)
      setShowDeleteConfirm(false)
      setDeleteTarget(null)
    } catch (error: any) {
      toast.error(`Error deleting ${deleteTarget.type}: ${error.message}`)
    } finally {
      setDeleteLoading(false)
    }
  }, [deleteTarget, fetchData])

  // Modal close handlers
  const handleCloseOrganizationForm = React.useCallback(() => setShowOrganizationForm(false), [])
  const handleCloseFacilityForm = React.useCallback(() => setShowFacilityForm(false), [])
  const handleCloseWorkplaceForm = React.useCallback(() => setShowWorkplaceForm(false), [])
  const handleCloseUserForm = React.useCallback(() => setShowUserForm(false), [])
  const handleCloseDeleteConfirm = React.useCallback(() => setShowDeleteConfirm(false), [])

  // User management functions
  const toggleUserStatus = React.useCallback(async (userId: string, currentStatus: boolean, userName: string) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ is_active: !currentStatus })
        .eq('id', userId)

      if (error) {
        throw error
      }

      await fetchData()
      toast.success(`User ${!currentStatus ? 'enabled' : 'disabled'} successfully!`)
    } catch (error: any) {
      console.warn('Error updating user status:', error)
      toast.error(`Error updating user status: ${error.message || 'Please try again.'}`)
    }
  }, [fetchData])

  const resetUserPassword = React.useCallback(async (userId: string, userEmail: string) => {
    if (!confirm(`Are you sure you want to reset the password for ${userEmail}?`)) {
      return
    }

    try {
      const generatePassword = () => {
        const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789'
        let password = ''
        for (let i = 0; i < 12; i++) {
          password += chars.charAt(Math.floor(Math.random() * chars.length))
        }
        return password
      }

      const newPassword = generatePassword()

      // Get the current session token
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        throw new Error('Not authenticated')
      }

      // Call the API route to reset password
      const response = await fetch('/api/admin/users', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          userId: userId,
          password: newPassword
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to reset password')
      }

      toast.success('Password reset successfully! New password generated.')
    } catch (error: any) {
      console.warn('Error resetting password:', error)
      toast.error(`Error resetting password: ${error.message || 'Please try again.'}`)
    }
  }, [])

  if (loading) {
    return <div className="text-center py-8">{t('common.loading')}</div>
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex justify-between items-center mb-6 flex-shrink-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {t('settings.title')}
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Manage your account and application settings.
          </p>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6 flex-shrink-0">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'profile', name: t('navigation.profile') },
            { id: 'organizations', name: t('settings.organizationSettings') },
            { id: 'facilities', name: t('settings.facilitySettings') },
            { id: 'workplaces', name: t('settings.workplaceSettings') },
            { id: 'users', name: t('users.userManagement') },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`${
                activeTab === tab.id
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`}
            >
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="flex-1 min-h-0">
        {activeTab === 'profile' && (
          <div className="bg-white shadow rounded-lg h-full">
            <div className="px-4 py-5 sm:p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                {t('navigation.profile')}
              </h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    {t('auth.email')}
                  </label>
                  <p className="mt-1 text-sm text-gray-900">{user?.email}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    {t('auth.fullName')}
                  </label>
                  <p className="mt-1 text-sm text-gray-900">{user?.full_name || 'Not set'}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'organizations' && (
          <div className="h-full flex flex-col">
            <div className="flex justify-between items-center mb-6 flex-shrink-0">
              <h2 className="text-lg font-medium text-gray-900">
                {t('settings.organizationSettings')}
              </h2>
              <Button onClick={handleCreateOrganization}>
                <PlusIcon className="w-4 h-4 mr-2" />
                Create Organization
              </Button>
            </div>

            <div className="flex-1 min-h-0">
              <DataTable
                data={organizations}
                fullHeight={true}
                title="Organizations"
                searchable={true}
                searchPlaceholder={`${t('common.search')} organizations...`}
                emptyMessage="No organizations found. Create an organization first."
                loading={loading}
                selectable={true}
                exportable={true}
                onExport={(data) => {
                  const csv = [
                    ['Name', 'Description', 'Facilities Count', 'Created Date'],
                    ...data.map(org => [
                      org.name,
                      org.description || '',
                      facilities.filter(f => f.organization_id === org.id).length.toString(),
                      new Date(org.created_at).toLocaleDateString()
                    ])
                  ].map(row => row.join(',')).join('\n')

                  const blob = new Blob([csv], { type: 'text/csv' })
                  const url = URL.createObjectURL(blob)
                  const a = document.createElement('a')
                  a.href = url
                  a.download = 'organizations.csv'
                  a.click()
                  URL.revokeObjectURL(url)
                }}
                columns={[
                  {
                    key: 'name',
                    header: t('organizations.organizationName'),
                    sortable: true,
                    render: (org) => (
                      <div className="flex items-center">
                        <BuildingOfficeIcon className="w-5 h-5 text-gray-400 mr-2" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">{org.name}</div>
                          {org.description && (
                            <div className="text-sm text-gray-500 max-w-xs truncate">{org.description}</div>
                          )}
                        </div>
                      </div>
                    )
                  },
                  {
                    key: 'facilities_count',
                    header: t('facilities.facilitiesCount'),
                    sortable: true,
                    render: (org) => {
                      const count = facilities.filter(f => f.organization_id === org.id).length
                      return <div className="text-sm text-gray-600">{count}</div>
                    }
                  },
                  {
                    key: 'created_at',
                    header: t('common.createdDate'),
                    sortable: true,
                    render: (org) => (
                      <div className="text-sm text-gray-500">
                        {new Date(org.created_at).toLocaleDateString()}
                      </div>
                    )
                  }
                ]}
                rowActions={[
                  {
                    label: t('common.edit'),
                    icon: PencilIcon,
                    onClick: (org) => handleEditOrganization(org),
                    variant: 'primary'
                  },
                  {
                    label: t('common.delete'),
                    icon: TrashIcon,
                    onClick: (org) => handleDeleteRequest('organization', org.id, org.name),
                    variant: 'danger'
                  }
                ]}
              />
            </div>
          </div>
        )}

        {activeTab === 'facilities' && (
          <div className="h-full flex flex-col">
            <div className="flex justify-between items-center mb-6 flex-shrink-0">
              <h2 className="text-lg font-medium text-gray-900">
                {t('settings.facilitySettings')}
              </h2>
              <Button onClick={handleCreateFacility}>
                <PlusIcon className="w-4 h-4 mr-2" />
                Create Facility
              </Button>
            </div>

            <div className="flex-1 min-h-0">
              <DataTable
                data={facilities}
                fullHeight={true}
                title="Facilities"
                searchable={true}
                searchPlaceholder={`${t('common.search')} facilities...`}
                emptyMessage="No facilities found. Create facilities first."
                loading={loading}
                selectable={true}
                exportable={true}
                onExport={(data) => {
                  const csv = [
                    ['Name', 'Organization', 'Description', 'Address', 'Created Date'],
                    ...data.map(facility => [
                      facility.name,
                      (facility as any).organization?.name || '',
                      facility.description || '',
                      facility.address || '',
                      new Date(facility.created_at).toLocaleDateString()
                    ])
                  ].map(row => row.join(',')).join('\n')

                  const blob = new Blob([csv], { type: 'text/csv' })
                  const url = URL.createObjectURL(blob)
                  const a = document.createElement('a')
                  a.href = url
                  a.download = 'facilities.csv'
                  a.click()
                  URL.revokeObjectURL(url)
                }}
                columns={[
                  {
                    key: 'name',
                    header: t('facilities.facilityName'),
                    sortable: true,
                    render: (facility) => (
                      <div className="flex items-center">
                        <BuildingOfficeIcon className="w-5 h-5 text-gray-400 mr-2" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">{facility.name}</div>
                          <div className="text-sm text-gray-500">
                            {(facility as any).organization?.name}
                          </div>
                        </div>
                      </div>
                    )
                  },
                  {
                    key: 'description',
                    header: t('facilities.description'),
                    sortable: true,
                    render: (facility) => (
                      <div className="text-sm text-gray-600 max-w-xs truncate">
                        {facility.description || '-'}
                      </div>
                    )
                  },
                  {
                    key: 'address',
                    header: t('facilities.address'),
                    sortable: true,
                    render: (facility) => (
                      <div className="flex items-center text-sm text-gray-600">
                        {facility.address ? (
                          <>
                            <MapPinIcon className="w-4 h-4 text-gray-400 mr-1" />
                            <span className="max-w-xs truncate">{facility.address}</span>
                          </>
                        ) : (
                          '-'
                        )}
                      </div>
                    )
                  },
                  {
                    key: 'created_at',
                    header: t('common.createdDate'),
                    sortable: true,
                    render: (facility) => (
                      <div className="text-sm text-gray-500">
                        {new Date(facility.created_at).toLocaleDateString()}
                      </div>
                    )
                  }
                ]}
                rowActions={[
                  {
                    label: t('common.edit'),
                    icon: PencilIcon,
                    onClick: (facility) => handleEditFacility(facility),
                    variant: 'primary'
                  },
                  {
                    label: t('workplaces.manageWorkplaces'),
                    icon: EyeIcon,
                    onClick: (facility) => {
                      setSelectedFacility(facility.id)
                      setActiveTab('workplaces')
                    }
                  },
                  {
                    label: t('common.delete'),
                    icon: TrashIcon,
                    onClick: (facility) => handleDeleteRequest('facility', facility.id, facility.name),
                    variant: 'danger'
                  }
                ]}
              />
            </div>
          </div>
        )}

        {activeTab === 'workplaces' && (
          <div className="h-full flex flex-col">
            <div className="flex justify-between items-center mb-6 flex-shrink-0">
              <h2 className="text-lg font-medium text-gray-900">
                {t('settings.workplaceSettings')}
              </h2>
              <Button onClick={handleCreateWorkplace}>
                <PlusIcon className="w-4 h-4 mr-2" />
                {t('workplaces.createWorkplace')}
              </Button>
            </div>

            <div className="flex-1 min-h-0">
              <DataTable
                data={workplaces.filter(workplace => !selectedFacility || workplace.facility_id === selectedFacility)}
                fullHeight={true}
                title="Workplaces"
                searchable={true}
                searchPlaceholder={`${t('common.search')} workplaces...`}
                emptyMessage="No workplaces found. Create workplaces within your facilities."
                loading={loading}
                selectable={true}
                exportable={true}
                onExport={(data) => {
                  const csv = [
                    ['Name', 'Facility', 'Description', 'Location', 'Created Date'],
                    ...data.map(workplace => [
                      workplace.name,
                      (workplace as any).facility?.name || '',
                      workplace.description || '',
                      workplace.location || '',
                      new Date(workplace.created_at).toLocaleDateString()
                    ])
                  ].map(row => row.join(',')).join('\n')

                  const blob = new Blob([csv], { type: 'text/csv' })
                  const url = URL.createObjectURL(blob)
                  const a = document.createElement('a')
                  a.href = url
                  a.download = 'workplaces.csv'
                  a.click()
                  URL.revokeObjectURL(url)
                }}
                columns={[
                  {
                    key: 'name',
                    header: t('workplaces.workplaceName'),
                    sortable: true,
                    render: (workplace) => (
                      <div className="flex items-center">
                        <MapPinIcon className="w-5 h-5 text-gray-400 mr-2" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">{workplace.name}</div>
                          <div className="text-sm text-gray-500">
                            {(workplace as any).facility?.name}
                          </div>
                        </div>
                      </div>
                    )
                  },
                  {
                    key: 'description',
                    header: t('workplaces.description'),
                    sortable: true,
                    render: (workplace) => (
                      <div className="text-sm text-gray-600 max-w-xs truncate">
                        {workplace.description || '-'}
                      </div>
                    )
                  },
                  {
                    key: 'location',
                    header: t('workplaces.location'),
                    sortable: true,
                    render: (workplace) => (
                      <div className="text-sm text-gray-600">
                        {workplace.location || '-'}
                      </div>
                    )
                  },
                  {
                    key: 'created_at',
                    header: t('common.createdDate'),
                    sortable: true,
                    render: (workplace) => (
                      <div className="text-sm text-gray-500">
                        {new Date(workplace.created_at).toLocaleDateString()}
                      </div>
                    )
                  }
                ]}
                rowActions={[
                  {
                    label: t('common.edit'),
                    icon: PencilIcon,
                    onClick: (workplace) => handleEditWorkplace(workplace),
                    variant: 'primary'
                  },
                  {
                    label: t('common.delete'),
                    icon: TrashIcon,
                    onClick: (workplace) => handleDeleteRequest('workplace', workplace.id, workplace.name),
                    variant: 'danger'
                  }
                ]}
              />
            </div>
          </div>
        )}

        {activeTab === 'users' && (
          <div className="h-full flex flex-col">
            <div className="flex justify-between items-center mb-6 flex-shrink-0">
              <h2 className="text-lg font-medium text-gray-900">
                {t('users.userManagement')}
              </h2>
              <Button onClick={handleCreateUser}>
                <PlusIcon className="w-4 h-4 mr-2" />
                {t('users.createUser')}
              </Button>
            </div>

            <div className="flex-1 min-h-0">
              <DataTable
                data={users}
                fullHeight={true}
                title="Users"
                searchable={true}
                searchPlaceholder={`${t('common.search')} users...`}
                emptyMessage={t('users.noUsers')}
                loading={loading}
                selectable={true}
                exportable={true}
                onExport={(data) => {
                  const csv = [
                    ['Name', 'Email', 'Account Number', 'Job Duties', 'Organization', 'Status', 'Created Date'],
                    ...data.map(user => [
                      user.full_name || '',
                      user.email,
                      user.account_number || '',
                      user.job_duties || '',
                      user.organization?.name || '',
                      user.is_active ? 'Active' : 'Inactive',
                      new Date(user.created_at).toLocaleDateString()
                    ])
                  ].map(row => row.join(',')).join('\n')

                  const blob = new Blob([csv], { type: 'text/csv' })
                  const url = URL.createObjectURL(blob)
                  const a = document.createElement('a')
                  a.href = url
                  a.download = 'users.csv'
                  a.click()
                  URL.revokeObjectURL(url)
                }}
                columns={[
                  {
                    key: 'full_name',
                    header: t('users.userFullName'),
                    sortable: true,
                    render: (user) => (
                      <div className="flex items-center">
                        <UserIcon className="w-5 h-5 text-gray-400 mr-2" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">{user.full_name}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </div>
                      </div>
                    )
                  },
                  {
                    key: 'account_number',
                    header: t('users.accountNumber'),
                    sortable: true,
                    render: (user) => user.account_number || '-'
                  },
                  {
                    key: 'job_duties',
                    header: t('users.jobDuties'),
                    sortable: true,
                    render: (user) => (
                      <div className="text-sm text-gray-600 max-w-xs truncate">
                        {user.job_duties || '-'}
                      </div>
                    )
                  },
                  {
                    key: 'organization',
                    header: t('users.organization'),
                    sortable: true,
                    render: (user) => user.organization?.name || '-'
                  },
                  {
                    key: 'is_active',
                    header: t('users.status'),
                    sortable: true,
                    render: (user) => (
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        user.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {user.is_active ? t('users.active') : t('users.inactive')}
                      </span>
                    )
                  }
                ]}
                rowActions={[
                  {
                    label: t('common.edit'),
                    icon: PencilIcon,
                    onClick: (user) => handleEditUser(user),
                    variant: 'primary'
                  },
                  {
                    label: t('users.toggleStatus'),
                    icon: UserMinusIcon,
                    onClick: (user) => toggleUserStatus(user.id, user.is_active || false, user.full_name || ''),
                    variant: 'secondary'
                  },
                  {
                    label: t('users.resetPassword'),
                    icon: KeyIcon,
                    onClick: (user) => resetUserPassword(user.id, user.email)
                  }
                ]}
              />
            </div>
          </div>
        )}
      </div>

      {/* Modal Components - Rendered at root level to prevent unmounting */}
      <OrganizationForm
        key="organization-form"
        isOpen={showOrganizationForm}
        onClose={handleCloseOrganizationForm}
        onSuccess={fetchData}
        organization={editingOrganization}
        mode={formMode}
      />

      <FacilityForm
        key="facility-form"
        isOpen={showFacilityForm}
        onClose={handleCloseFacilityForm}
        onSuccess={fetchData}
        facility={editingFacility}
        organizations={organizations}
        mode={formMode}
      />

      <WorkplaceForm
        key="workplace-form"
        isOpen={showWorkplaceForm}
        onClose={handleCloseWorkplaceForm}
        onSuccess={fetchData}
        workplace={editingWorkplace}
        facilities={facilities}
        mode={formMode}
        selectedFacilityId={selectedFacility}
      />

      <UserForm
        key="user-form"
        isOpen={showUserForm}
        onClose={handleCloseUserForm}
        onSuccess={fetchData}
        user={editingUser}
        organizations={organizations}
        mode={formMode}
      />

      <ConfirmDialog
        key="delete-confirm"
        isOpen={showDeleteConfirm}
        onClose={handleCloseDeleteConfirm}
        onConfirm={handleDeleteConfirm}
        title={`Delete ${deleteTarget?.type}`}
        message={`Are you sure you want to delete "${deleteTarget?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
        loading={deleteLoading}
      />
    </div>
  )
}
