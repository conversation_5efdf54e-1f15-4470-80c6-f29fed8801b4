'use client'

import React, { useState, useEffect } from 'react'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/button'
import { supabase, type Organization } from '@/lib/supabase'
import toast from 'react-hot-toast'

interface User {
  id: string
  email: string
  full_name?: string
  account_number?: string
  job_duties?: string
  is_active?: boolean
  organization?: {
    id: string
    name: string
  }
}

export interface UserFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  user?: User | null
  organizations: Organization[]
  mode: 'create' | 'edit'
}

export function UserForm({
  isOpen,
  onClose,
  onSuccess,
  user,
  organizations,
  mode
}: UserFormProps) {
  const [formData, setFormData] = useState({
    email: '',
    full_name: '',
    account_number: '',
    job_duties: '',
    organization_id: '',
    is_active: true
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (user && mode === 'edit') {
      setFormData({
        email: user.email || '',
        full_name: user.full_name || '',
        account_number: user.account_number || '',
        job_duties: user.job_duties || '',
        organization_id: user.organization?.id || '',
        is_active: user.is_active ?? true
      })
    } else {
      setFormData({
        email: '',
        full_name: '',
        account_number: '',
        job_duties: '',
        organization_id: organizations.length > 0 ? organizations[0].id : '',
        is_active: true
      })
    }
    setErrors({})
  }, [user, mode, isOpen, organizations])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Full name is required'
    }

    if (!formData.organization_id) {
      newErrors.organization_id = 'Organization is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      if (mode === 'create') {
        // For create mode, we would typically use Supabase Auth
        // For now, we'll just insert into the users table
        const { error } = await supabase
          .from('users')
          .insert([{
            email: formData.email.trim(),
            full_name: formData.full_name.trim(),
            account_number: formData.account_number.trim() || null,
            job_duties: formData.job_duties.trim() || null,
            is_active: formData.is_active
          }])

        if (error) throw error

        // Also create user role
        const { error: roleError } = await supabase
          .from('user_roles')
          .insert([{
            user_id: formData.email, // This would be the actual user ID in a real implementation
            organization_id: formData.organization_id,
            role: 'member'
          }])

        if (roleError) throw roleError
        toast.success('User created successfully')
      } else {
        const { error } = await supabase
          .from('users')
          .update({
            email: formData.email.trim(),
            full_name: formData.full_name.trim(),
            account_number: formData.account_number.trim() || null,
            job_duties: formData.job_duties.trim() || null,
            is_active: formData.is_active
          })
          .eq('id', user!.id)

        if (error) throw error
        toast.success('User updated successfully')
      }

      onSuccess()
      onClose()
    } catch (error: any) {
      toast.error(`Error ${mode === 'create' ? 'creating' : 'updating'} user: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = React.useCallback(() => {
    if (!loading) {
      onClose()
    }
  }, [loading, onClose])

  const handleOrganizationChange = React.useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData(prev => ({ ...prev, organization_id: e.target.value }))
  }, [])

  const handleEmailChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, email: e.target.value }))
  }, [])

  const handleFullNameChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, full_name: e.target.value }))
  }, [])

  const handleAccountNumberChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, account_number: e.target.value }))
  }, [])

  const handleJobDutiesChange = React.useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, job_duties: e.target.value }))
  }, [])

  const handleIsActiveChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, is_active: e.target.checked }))
  }, [])

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={mode === 'create' ? 'Create User' : 'Edit User'}
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="organization_id" className="block text-sm font-medium text-gray-700">
            Organization *
          </label>
          <select
            id="organization_id"
            value={formData.organization_id}
            onChange={handleOrganizationChange}
            className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
              errors.organization_id ? 'border-red-300' : 'border-gray-300'
            }`}
            disabled={loading}
          >
            <option value="">Select an organization</option>
            {organizations.map((org) => (
              <option key={org.id} value={org.id}>
                {org.name}
              </option>
            ))}
          </select>
          {errors.organization_id && (
            <p className="mt-1 text-sm text-red-600">{errors.organization_id}</p>
          )}
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            Email Address *
          </label>
          <input
            type="email"
            id="email"
            value={formData.email}
            onChange={handleEmailChange}
            className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
              errors.email ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Enter email address"
            disabled={loading || mode === 'edit'}
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email}</p>
          )}
          {mode === 'edit' && (
            <p className="mt-1 text-sm text-gray-500">Email cannot be changed after creation</p>
          )}
        </div>

        <div>
          <label htmlFor="full_name" className="block text-sm font-medium text-gray-700">
            Full Name *
          </label>
          <input
            type="text"
            id="full_name"
            value={formData.full_name}
            onChange={handleFullNameChange}
            className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
              errors.full_name ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Enter full name"
            disabled={loading}
          />
          {errors.full_name && (
            <p className="mt-1 text-sm text-red-600">{errors.full_name}</p>
          )}
        </div>

        <div>
          <label htmlFor="account_number" className="block text-sm font-medium text-gray-700">
            Account Number
          </label>
          <input
            type="text"
            id="account_number"
            value={formData.account_number}
            onChange={handleAccountNumberChange}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            placeholder="Enter account number (optional)"
            disabled={loading}
          />
        </div>

        <div>
          <label htmlFor="job_duties" className="block text-sm font-medium text-gray-700">
            Job Duties
          </label>
          <textarea
            id="job_duties"
            rows={3}
            value={formData.job_duties}
            onChange={handleJobDutiesChange}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            placeholder="Enter job duties (optional)"
            disabled={loading}
          />
        </div>

        <div className="flex items-center">
          <input
            id="is_active"
            type="checkbox"
            checked={formData.is_active}
            onChange={handleIsActiveChange}
            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
            disabled={loading}
          />
          <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
            Active user
          </label>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading}
          >
            {loading ? 'Saving...' : mode === 'create' ? 'Create User' : 'Update User'}
          </Button>
        </div>
      </form>
    </Modal>
  )
}
