'use client'

import { useState, useEffect, use } from 'react'
import { useTranslations } from 'next-intl'
import { useUser } from '@/components/auth/user-provider'
import { Button } from '@/components/ui/button'
import { inspectionService } from '@/lib/inspection-service'
import { supabase, type InspectionPoint, type WorkplaceInspection, type InspectionResult, type Organization, type Facility, type Workplace } from '@/lib/supabase'
import toast from 'react-hot-toast'
import {
  CheckCircleIcon,
  XCircleIcon,
  ClipboardDocumentCheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

export default function ConductInspectionPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = use(params)
  const t = useTranslations()
  const { user } = useUser()

  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [facilities, setFacilities] = useState<Facility[]>([])
  const [workplaces, setWorkplaces] = useState<Workplace[]>([])
  const [inspectionPoints, setInspectionPoints] = useState<InspectionPoint[]>([])
  const [currentInspection, setCurrentInspection] = useState<WorkplaceInspection | null>(null)
  const [inspectionResults, setInspectionResults] = useState<Record<string, { is_compliant: boolean; notes: string }>>({})
  
  const [selectedOrganization, setSelectedOrganization] = useState('')
  const [selectedFacility, setSelectedFacility] = useState('')
  const [selectedWorkplace, setSelectedWorkplace] = useState('')
  const [inspectionNotes, setInspectionNotes] = useState('')

  useEffect(() => {
    if (user) {
      fetchData()
    }
  }, [user])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      // Fetch organizations, facilities, and workplaces
      const [orgsData, facilitiesData, workplacesData] = await Promise.all([
        supabase.from('organizations').select('*').order('name'),
        supabase.from('facilities').select('*').order('name'),
        supabase.from('workplaces').select('*').order('name')
      ])

      if (orgsData.error) throw orgsData.error
      if (facilitiesData.error) throw facilitiesData.error
      if (workplacesData.error) throw workplacesData.error

      setOrganizations(orgsData.data || [])
      setFacilities(facilitiesData.data || [])
      setWorkplaces(workplacesData.data || [])
    } catch (error: any) {
      toast.error(`Error loading data: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleWorkplaceChange = async (workplaceId: string) => {
    setSelectedWorkplace(workplaceId)
    setCurrentInspection(null)
    setInspectionResults({})
    setInspectionNotes('')

    if (workplaceId) {
      try {
        // Fetch inspection points for this workplace
        const points = await inspectionService.getInspectionPoints({
          workplaceId,
          isActive: true
        })
        setInspectionPoints(points)

        // Initialize inspection results
        const initialResults: Record<string, { is_compliant: boolean; notes: string }> = {}
        points.forEach(point => {
          initialResults[point.id] = { is_compliant: false, notes: '' }
        })
        setInspectionResults(initialResults)
      } catch (error: any) {
        toast.error(`Error loading inspection points: ${error.message}`)
      }
    } else {
      setInspectionPoints([])
    }
  }

  const handleStartInspection = async () => {
    if (!selectedWorkplace) {
      toast.error('Please select a workplace first')
      return
    }

    try {
      const inspection = await inspectionService.createWorkplaceInspection({
        workplace_id: selectedWorkplace,
        notes: inspectionNotes
      })

      if (inspection) {
        setCurrentInspection(inspection)
        toast.success('Inspection started successfully')
      }
    } catch (error: any) {
      toast.error(`Error starting inspection: ${error.message}`)
    }
  }

  const handleResultChange = (pointId: string, field: 'is_compliant' | 'notes', value: boolean | string) => {
    setInspectionResults(prev => ({
      ...prev,
      [pointId]: {
        ...prev[pointId],
        [field]: value
      }
    }))
  }

  const handleSubmitInspection = async () => {
    if (!currentInspection) {
      toast.error('No active inspection found')
      return
    }

    // Validate that all points have been checked
    const uncheckedPoints = inspectionPoints.filter(point => 
      inspectionResults[point.id]?.is_compliant === undefined
    )

    if (uncheckedPoints.length > 0) {
      toast.error('Please complete all inspection points before submitting')
      return
    }

    setSubmitting(true)

    try {
      // Save all inspection results
      const resultPromises = inspectionPoints.map(point => 
        inspectionService.createInspectionResult({
          workplace_inspection_id: currentInspection.id,
          inspection_point_id: point.id,
          is_compliant: inspectionResults[point.id].is_compliant,
          notes: inspectionResults[point.id].notes || undefined
        })
      )

      await Promise.all(resultPromises)

      // Update inspection status to completed
      const allCompliant = inspectionPoints.every(point => 
        inspectionResults[point.id].is_compliant
      )

      await inspectionService.updateWorkplaceInspection(currentInspection.id, {
        status: allCompliant ? 'completed' : 'failed',
        notes: inspectionNotes
      })

      toast.success('Inspection submitted successfully')
      
      // Reset form
      setCurrentInspection(null)
      setInspectionResults({})
      setInspectionNotes('')
      setSelectedWorkplace('')
      setInspectionPoints([])
    } catch (error: any) {
      toast.error(`Error submitting inspection: ${error.message}`)
    } finally {
      setSubmitting(false)
    }
  }

  const filteredFacilities = facilities.filter(facility => 
    !selectedOrganization || facility.organization_id === selectedOrganization
  )

  const filteredWorkplaces = workplaces.filter(workplace => 
    !selectedFacility || workplace.facility_id === selectedFacility
  )

  const completedPoints = inspectionPoints.filter(point => 
    inspectionResults[point.id]?.is_compliant !== undefined
  ).length

  const compliantPoints = inspectionPoints.filter(point => 
    inspectionResults[point.id]?.is_compliant === true
  ).length

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Conduct Workplace Inspection</h1>
          <p className="mt-1 text-sm text-gray-500">
            Perform safety inspections and record compliance status
          </p>
        </div>
      </div>

      {/* Workplace Selection */}
      <div className="bg-white p-6 rounded-lg shadow border">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Select Workplace</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Organization
            </label>
            <select
              value={selectedOrganization}
              onChange={(e) => {
                setSelectedOrganization(e.target.value)
                setSelectedFacility('')
                setSelectedWorkplace('')
              }}
              className="block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-orange-500 focus:outline-none focus:ring-orange-500"
              disabled={currentInspection !== null}
            >
              <option value="">Select Organization</option>
              {organizations.map((org) => (
                <option key={org.id} value={org.id}>
                  {org.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Facility
            </label>
            <select
              value={selectedFacility}
              onChange={(e) => {
                setSelectedFacility(e.target.value)
                setSelectedWorkplace('')
              }}
              disabled={!selectedOrganization || currentInspection !== null}
              className="block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-orange-500 focus:outline-none focus:ring-orange-500 disabled:bg-gray-100"
            >
              <option value="">Select Facility</option>
              {filteredFacilities.map((facility) => (
                <option key={facility.id} value={facility.id}>
                  {facility.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Workplace
            </label>
            <select
              value={selectedWorkplace}
              onChange={(e) => handleWorkplaceChange(e.target.value)}
              disabled={!selectedFacility || currentInspection !== null}
              className="block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-orange-500 focus:outline-none focus:ring-orange-500 disabled:bg-gray-100"
            >
              <option value="">Select Workplace</option>
              {filteredWorkplaces.map((workplace) => (
                <option key={workplace.id} value={workplace.id}>
                  {workplace.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {selectedWorkplace && !currentInspection && (
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Inspection Notes
            </label>
            <textarea
              value={inspectionNotes}
              onChange={(e) => setInspectionNotes(e.target.value)}
              rows={3}
              className="block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-orange-500 focus:outline-none focus:ring-orange-500"
              placeholder="Enter any general notes about this inspection..."
            />
            <div className="mt-4">
              <Button
                onClick={handleStartInspection}
                className="flex items-center space-x-2"
              >
                <ClipboardDocumentCheckIcon className="h-4 w-4" />
                <span>Start Inspection</span>
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Inspection Progress */}
      {currentInspection && (
        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Inspection Progress</h3>
            <div className="text-sm text-gray-600">
              {completedPoints} of {inspectionPoints.length} points completed
            </div>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div 
              className="bg-orange-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${inspectionPoints.length > 0 ? (completedPoints / inspectionPoints.length) * 100 : 0}%` }}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <CheckCircleIcon className="h-5 w-5 text-green-500" />
              <span>Compliant: {compliantPoints}</span>
            </div>
            <div className="flex items-center space-x-2">
              <XCircleIcon className="h-5 w-5 text-red-500" />
              <span>Non-compliant: {completedPoints - compliantPoints}</span>
            </div>
            <div className="flex items-center space-x-2">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
              <span>Remaining: {inspectionPoints.length - completedPoints}</span>
            </div>
          </div>
        </div>
      )}

      {/* Inspection Points */}
      {currentInspection && inspectionPoints.length > 0 && (
        <div className="bg-white rounded-lg shadow border">
          <div className="p-6 border-b">
            <h3 className="text-lg font-medium text-gray-900">Inspection Checklist</h3>
            <p className="mt-1 text-sm text-gray-500">
              Check each inspection point and mark as compliant or non-compliant
            </p>
          </div>

          <div className="divide-y divide-gray-200">
            {inspectionPoints.map((point, index) => (
              <div key={point.id} className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full text-sm font-medium text-gray-600">
                      {index + 1}
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <h4 className="text-lg font-medium text-gray-900">{point.inspection_name}</h4>
                    {point.description && (
                      <p className="mt-1 text-sm text-gray-600">{point.description}</p>
                    )}
                    {point.image_url && (
                      <img 
                        src={point.image_url} 
                        alt={point.inspection_name}
                        className="mt-2 max-w-xs rounded-lg border"
                      />
                    )}
                    
                    <div className="mt-4 space-y-3">
                      <div className="flex space-x-4">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name={`compliance-${point.id}`}
                            checked={inspectionResults[point.id]?.is_compliant === true}
                            onChange={() => handleResultChange(point.id, 'is_compliant', true)}
                            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                          />
                          <span className="ml-2 text-sm text-green-700 font-medium">Compliant</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name={`compliance-${point.id}`}
                            checked={inspectionResults[point.id]?.is_compliant === false}
                            onChange={() => handleResultChange(point.id, 'is_compliant', false)}
                            className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                          />
                          <span className="ml-2 text-sm text-red-700 font-medium">Non-compliant</span>
                        </label>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Notes (optional)
                        </label>
                        <textarea
                          value={inspectionResults[point.id]?.notes || ''}
                          onChange={(e) => handleResultChange(point.id, 'notes', e.target.value)}
                          rows={2}
                          className="block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-orange-500 focus:outline-none focus:ring-orange-500"
                          placeholder="Add any notes about this inspection point..."
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="p-6 border-t bg-gray-50">
            <div className="flex justify-end">
              <Button
                onClick={handleSubmitInspection}
                disabled={submitting || completedPoints < inspectionPoints.length}
                className="flex items-center space-x-2"
              >
                <ClipboardDocumentCheckIcon className="h-4 w-4" />
                <span>{submitting ? 'Submitting...' : 'Submit Inspection'}</span>
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* No inspection points message */}
      {selectedWorkplace && inspectionPoints.length === 0 && !currentInspection && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                No Inspection Points Found
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  There are no active inspection points configured for this workplace. 
                  Please contact your administrator to set up inspection points.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
